const chalk = require('chalk')

/**
 * PromptResponseHandler - 统一的提示词和响应处理器
 *
 * 负责：
 * - 格式化提示词中的XML模板
 * - 解析AI响应中的XML内容
 * - 从markdown代码块中提取XML
 * - 统一处理不同的XML格式
 */
class PromptResponseHandler {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      maxContentLength: 10000,
      ...options
    };

    // 支持的XML格式定义
    this.formats = {
      // 文件修复格式
      fix_result: {
        tag: 'fix_result',
        contentTag: 'fixed_content',
        metaTag: 'changes_made'
      },
      // 文件修复格式（旧版本兼容）
      file_fix: {
        tag: 'file_fix',
        contentTag: 'content',
        metaTag: null
      },
      // 分析结果格式
      analysis: {
        tag: 'analysis',
        contentTag: 'files_to_fix',
        metaTag: 'reasoning'
      },
      // 工具调用格式
      tool_calls: {
        tag: 'tool_calls',
        contentTag: null,
        metaTag: null
      }
    };
  }

  /**
   * 格式化XML模板 - 用于PromptBuilder
   */
  formatTemplate(templateType, content, metadata = null) {
    const format = this.formats[templateType];
    if (!format) {
      throw new Error(`不支持的模板类型: ${templateType}`);
    }

    let xml = `<${format.tag}>`;

    if (format.contentTag) {
      xml += `\n<${format.contentTag}>\n${content}\n</${format.contentTag}>`;
    } else {
      xml += `\n${content}`;
    }

    if (metadata && format.metaTag) {
      xml += `\n<${format.metaTag}>\n${metadata}\n</${format.metaTag}>`;
    }

    xml += `\n</${format.tag}>`;

    return xml;
  }

  /**
   * 解析工具调用响应
   */
  parseToolCallsResponse(response) {
    try {
      // 尝试解析JSON格式
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        const jsonData = JSON.parse(jsonMatch[1]);
        if (jsonData.tool_calls && Array.isArray(jsonData.tool_calls)) {
          return jsonData.tool_calls;
        }
      }

      // 尝试解析XML格式（向后兼容）
      const xmlMatch = response.match(/<tool_calls>([\s\S]*?)<\/tool_calls>/);
      if (xmlMatch) {
        const toolCalls = [];
        const callsSection = xmlMatch[1];
        const callMatches = callsSection.match(/<call>([\s\S]*?)<\/call>/g);

        if (callMatches) {
          for (const callMatch of callMatches) {
            const nameMatch = callMatch.match(/<n>(.*?)<\/n>/);
            const parametersMatch = callMatch.match(/<parameters>([\s\S]*?)<\/parameters>/);

            if (nameMatch && parametersMatch) {
              const toolName = nameMatch[1].trim();
              const parameters = {};

              // 解析参数
              const paramSection = parametersMatch[1];
              const paramMatches = paramSection.match(/<(\w+)>(.*?)<\/\w+>/g);

              if (paramMatches) {
                for (const paramMatch of paramMatches) {
                  const paramKeyMatch = paramMatch.match(/<(\w+)>(.*?)<\/\w+>/);
                  if (paramKeyMatch) {
                    parameters[paramKeyMatch[1]] = paramKeyMatch[2].trim();
                  }
                }
              }

              toolCalls.push({
                name: toolName,
                parameters
              });
            }
          }
        }
        return toolCalls;
      }

      return [];
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析工具调用响应失败'));
      return [];
    }
  }


  /**
   * 解析AI响应中的XML内容
   */
  parseResponse(response, expectedFormat = null) {
    try {
      if (this.options.verbose) {
        console.log(`🔍 开始解析AI响应，长度: ${response.length} 字符`);
      }

      // 首先尝试从markdown代码块中提取
      const extractedXml = this.extractFromCodeBlock(response);
      const contentToParse = extractedXml || response;

      // 如果指定了期望格式，优先尝试该格式
      if (expectedFormat && this.formats[expectedFormat]) {
        const result = this.parseSpecificFormat(contentToParse, expectedFormat);
        if (result.success) {
          return result;
        }
      }

      // 尝试所有支持的格式
      for (const [formatName, format] of Object.entries(this.formats)) {
        const result = this.parseSpecificFormat(contentToParse, formatName);
        if (result.success) {
          if (this.options.verbose) {
            console.log(`✅ 成功解析格式: ${formatName}`);
          }
          return result;
        }
      }

      // 如果所有格式都失败，尝试通用代码块解析
      const codeBlockResult = this.parseCodeBlock(response);
      if (codeBlockResult.success) {
        return codeBlockResult;
      }

      return {
        success: false,
        error: '无法解析AI响应格式',
        format: null,
        content: null,
        metadata: null
      };

    } catch (error) {
      return {
        success: false,
        error: `解析响应失败: ${error.message}`,
        format: null,
        content: null,
        metadata: null
      };
    }
  }

  /**
   * 解析特定格式的XML
   */
  parseSpecificFormat(content, formatName) {
    const format = this.formats[formatName];
    if (!format) {
      return { success: false, error: `不支持的格式: ${formatName}` };
    }

    // 构建正则表达式
    const tagPattern = `<${format.tag}>[\\s\\S]*?</${format.tag}>`;
    const match = content.match(new RegExp(tagPattern));

    if (!match) {
      return { success: false, error: `未找到 ${format.tag} 标签` };
    }

    const xmlContent = match[0];
    let parsedContent = null;
    let metadata = null;

    // 解析内容
    if (format.contentTag) {
      const contentPattern = `<${format.contentTag}>([\\s\\S]*?)</${format.contentTag}>`;
      const contentMatch = xmlContent.match(new RegExp(contentPattern));
      if (contentMatch) {
        parsedContent = this.decodeHtmlEntities(contentMatch[1].trim());
      }
    } else {
      // 如果没有内容标签，提取标签内的所有内容
      const innerPattern = `<${format.tag}>([\\s\\S]*?)</${format.tag}>`;
      const innerMatch = xmlContent.match(new RegExp(innerPattern));
      if (innerMatch) {
        parsedContent = this.decodeHtmlEntities(innerMatch[1].trim());
      }
    }

    // 解析元数据
    if (format.metaTag) {
      const metaPattern = `<${format.metaTag}>([\\s\\S]*?)</${format.metaTag}>`;
      const metaMatch = xmlContent.match(new RegExp(metaPattern));
      if (metaMatch) {
        metadata = metaMatch[1].trim();
      }
    }

    if (parsedContent) {
      return {
        success: true,
        format: formatName,
        content: parsedContent,
        metadata: metadata,
        rawXml: xmlContent
      };
    }

    return { success: false, error: `无法解析 ${formatName} 格式的内容` };
  }

  /**
   * 从markdown代码块中提取XML
   */
  extractFromCodeBlock(response) {
    const codeBlockPattern = /```(?:xml)?\s*([\s\S]*?)\s*```/;
    const match = response.match(codeBlockPattern);

    if (match) {
      const content = match[1].trim();
      // 检查是否包含XML标签
      if (content.includes('<') && content.includes('>')) {
        if (this.options.verbose) {
          console.log(`📦 从代码块中提取XML，长度: ${content.length} 字符`);
        }
        return content;
      }
    }

    return null;
  }

  /**
   * 解析通用代码块（回退方案）
   */
  parseCodeBlock(response) {
    const codeBlockPattern = /```(?:vue|js|ts|javascript|typescript|xml)?\s*([\s\S]*?)\s*```/;
    const match = response.match(codeBlockPattern);

    if (match) {
      const content = this.decodeHtmlEntities(match[1].trim());

      if (this.options.verbose) {
        console.log(`📝 解析通用代码块，长度: ${content.length} 字符`);
      }

      return {
        success: true,
        format: 'code_block',
        content: content,
        metadata: null,
        rawXml: null
      };
    }

    return { success: false, error: '未找到代码块' };
  }

  /**
   * 解码HTML实体
   */
  decodeHtmlEntities(text) {
    const entities = {
      '&lt;': '<',
      '&gt;': '>',
      '&amp;': '&',
      '&quot;': '"',
      '&#39;': "'",
      '&apos;': "'"
    };

    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 验证XML格式是否正确
   */
  validateXmlFormat(content, formatName) {
    const format = this.formats[formatName];
    if (!format) {
      return false;
    }

    // 检查是否包含必要的标签
    const hasMainTag = content.includes(`<${format.tag}>`) && content.includes(`</${format.tag}>`);

    if (format.contentTag) {
      const hasContentTag = content.includes(`<${format.contentTag}>`) && content.includes(`</${format.contentTag}>`);
      return hasMainTag && hasContentTag;
    }

    return hasMainTag;
  }

  /**
   * 获取支持的格式列表
   */
  getSupportedFormats() {
    return Object.keys(this.formats);
  }

  /**
   * 生成格式示例
   */
  generateFormatExample(formatName, sampleContent = '示例内容', sampleMetadata = '示例说明') {
    const format = this.formats[formatName];
    if (!format) {
      throw new Error(`不支持的格式: ${formatName}`);
    }

    return this.formatTemplate(formatName, sampleContent, sampleMetadata);
  }

  /**
   * 解析文件列表（用于analysis格式）
   */
  parseFileList(content) {
    const files = [];

    // 匹配 <file>path</file> 格式
    const fileMatches = content.match(/<file>(.*?)<\/file>/g);
    if (fileMatches) {
      fileMatches.forEach(match => {
        const pathMatch = match.match(/<file>(.*?)<\/file>/);
        if (pathMatch) {
          const filePath = pathMatch[1].trim();
          if (filePath) {
            files.push(filePath);
          }
        }
      });
    }

    // 匹配 <item type="file">path</item> 格式
    const itemMatches = content.match(/<item\s+type="(file|directory)">(.*?)<\/item>/g);
    if (itemMatches) {
      itemMatches.forEach(match => {
        const typeMatch = match.match(/type="(file|directory)"/);
        const pathMatch = match.match(/<item[^>]*>(.*?)<\/item>/);

        if (typeMatch && pathMatch) {
          const type = typeMatch[1];
          const filePath = pathMatch[1].trim();
          if (filePath) {
            files.push({ type, path: filePath });
          }
        }
      });
    }

    return files;
  }

  /**
   * 解析工具调用（JSON格式）
   */
  parseToolCalls(response) {
    try {
      // 首先尝试从代码块中提取JSON
      const jsonMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      let jsonContent = jsonMatch ? jsonMatch[1].trim() : response;

      // 尝试解析JSON
      const parsed = JSON.parse(jsonContent);

      if (parsed.tool_calls && Array.isArray(parsed.tool_calls)) {
        return {
          success: true,
          toolCalls: parsed.tool_calls,
          reasoning: parsed.reasoning || null
        };
      }

      return { success: false, error: '未找到有效的tool_calls数组' };
    } catch (error) {
      // 回退到XML格式解析
      return this.parseXmlToolCalls(response);
    }
  }

  /**
   * 解析XML格式的工具调用（向后兼容）
   */
  parseXmlToolCalls(response) {
    try {
      const xmlMatch = response.match(/<tool_calls>([\s\S]*?)<\/tool_calls>/);
      if (!xmlMatch) {
        return { success: false, error: '未找到tool_calls标签' };
      }

      const toolCalls = [];
      const callsSection = xmlMatch[1];
      const callMatches = callsSection.match(/<call>([\s\S]*?)<\/call>/g);

      if (callMatches) {
        for (const callMatch of callMatches) {
          const nameMatch = callMatch.match(/<n>(.*?)<\/n>/);
          const parametersMatch = callMatch.match(/<parameters>([\s\S]*?)<\/parameters>/);

          if (nameMatch && parametersMatch) {
            const toolName = nameMatch[1].trim();
            const parameters = {};

            // 解析参数
            const paramSection = parametersMatch[1];
            const paramMatches = paramSection.match(/<(\w+)>(.*?)<\/\w+>/g);

            if (paramMatches) {
              for (const paramMatch of paramMatches) {
                const paramKeyMatch = paramMatch.match(/<(\w+)>(.*?)<\/\w+>/);
                if (paramKeyMatch) {
                  parameters[paramKeyMatch[1]] = paramKeyMatch[2].trim();
                }
              }
            }

            toolCalls.push({
              name: toolName,
              parameters
            });
          }
        }
      }

      return {
        success: true,
        toolCalls: toolCalls,
        reasoning: null
      };
    } catch (error) {
      return {
        success: false,
        error: `解析XML工具调用失败: ${error.message}`
      };
    }
  }

  /**
   * 统一的响应解析入口
   */
  parseAnyResponse(response, expectedType = null) {
    // 根据期望类型选择解析方法
    switch (expectedType) {
      case 'tool_calls':
        return this.parseToolCalls(response);
      case 'file_list':
        const analysisResult = this.parseResponse(response, 'analysis');
        if (analysisResult.success) {
          const files = this.parseFileList(analysisResult.content);
          return {
            success: true,
            files: files,
            reasoning: analysisResult.metadata
          };
        }
        return { success: false, error: '无法解析文件列表' };
      default:
        return this.parseResponse(response, expectedType);
    }
  }
}

module.exports = PromptResponseHandler;
