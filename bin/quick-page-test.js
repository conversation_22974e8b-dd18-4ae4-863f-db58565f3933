#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { Command } = require('commander');
const PageValidator = require('../src/runtime-validation/PageValidator');

const program = new Command();

program
  .name('quick-page-test')
  .description('快速页面错误检测工具')
  .version('1.0.0');

program
  .command('test')
  .description('快速测试指定页面')
  .argument('<url>', '要测试的页面URL (如: /charts/keyboard)')
  .option('-p, --port <port>', '开发服务器端口', '3000')
  .option('--base-url <url>', '服务器基础URL', 'http://localhost:3000')
  .option('--timeout <timeout>', '页面超时时间(ms)', '10000')
  .option('--headless', '无头模式运行浏览器', true)
  .option('--no-headless', '显示浏览器界面')
  .option('-v, --verbose', '详细输出')
  .action(async (url, options) => {
    try {
      await runQuickTest(url, options);
    } catch (error) {
      console.error(chalk.red(`❌ 测试失败: ${error.message}`));
      process.exit(1);
    }
  });

/**
 * 执行快速页面测试
 */
async function runQuickTest(url, options) {
  console.log(chalk.blue('🚀 快速页面错误检测'));
  console.log(chalk.gray(`   测试URL: ${options.baseUrl}${url}`));

  // 创建虚拟路由
  const routes = [{ 
    path: url, 
    name: 'QuickTest',
    url: url
  }];

  // 创建页面验证器 - 使用优化的设置
  const validator = new PageValidator(process.cwd(), routes, {
    baseUrl: options.baseUrl,
    port: parseInt(options.port),
    timeout: parseInt(options.timeout),
    pageTimeout: parseInt(options.timeout),
    headless: options.headless,
    verbose: options.verbose,
    // 快速模式优化设置
    waitForServer: 5000,  // 减少服务器等待时间
    autoFix: false,       // 禁用自动修复以加快速度
    revalidateAfterFix: false
  });

  console.log(chalk.gray('   启动浏览器...'));
  
  const startTime = Date.now();
  const result = await validator.validateAllPages();
  const duration = Date.now() - startTime;

  if (result.success && result.results.length > 0) {
    const pageResult = result.results[0];
    
    console.log(chalk.blue(`\n📊 测试结果 (耗时: ${duration}ms)`));
    console.log(chalk.gray(`   页面: ${pageResult.url}`));
    console.log(chalk.gray(`   加载时间: ${pageResult.loadTime}ms`));
    
    if (pageResult.success) {
      console.log(chalk.green(`   状态: ✅ 通过`));
    } else {
      console.log(chalk.red(`   状态: ❌ 失败`));
      
      if (pageResult.errors.length > 0) {
        console.log(chalk.red('\n🚨 发现的错误:'));
        pageResult.errors.forEach((error, index) => {
          console.log(chalk.red(`   ${index + 1}. ${error}`));
        });
      }
    }
    
    if (pageResult.warnings.length > 0) {
      console.log(chalk.yellow('\n⚠️  警告信息:'));
      pageResult.warnings.forEach((warning, index) => {
        console.log(chalk.yellow(`   ${index + 1}. ${warning}`));
      });
    }

    if (pageResult.consoleMessages.length > 0 && options.verbose) {
      console.log(chalk.gray('\n📝 控制台消息:'));
      pageResult.consoleMessages.forEach((msg, index) => {
        const color = msg.type === 'error' ? chalk.red : 
                     msg.type === 'warning' ? chalk.yellow : chalk.gray;
        console.log(color(`   ${index + 1}. [${msg.type}] ${msg.text}`));
      });
    }

    if (pageResult.networkErrors.length > 0 && options.verbose) {
      console.log(chalk.gray('\n🌐 网络错误:'));
      pageResult.networkErrors.forEach((error, index) => {
        console.log(chalk.red(`   ${index + 1}. ${error.url} - ${error.failure}`));
      });
    }

  } else {
    console.log(chalk.red('\n❌ 页面验证失败'));
    if (result.errors && result.errors.length > 0) {
      console.log(chalk.red('错误信息:'));
      result.errors.forEach(error => {
        console.log(chalk.red(`   - ${error}`));
      });
    }
    process.exit(1);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red(`❌ 未捕获的异常: ${error.message}`));
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red(`❌ 未处理的 Promise 拒绝: ${reason}`));
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  收到中断信号，正在清理资源...'));
  process.exit(0);
});

program.parse();
